<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickEsti - Estimation de projet web</title>
    <meta name="description" content="Estimez rapidement le temps et le coût de votre projet web avec QuickEsti. Outil d'estimation intelligent pour développeurs et entreprises.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class', // Nécessaire pour activer le dark mode via la classe "dark"
            theme: {
                extend: {},
            },
            plugins: [],
        }
    </script>
    
    <!-- Flowbite CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Custom CSS -->
    <style>
        [v-cloak] { display: none; }
        
        /* Custom Tailwind config */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Animation de collapse/expand pour les sections avancées */
        .expand-transition {
            transition: max-height 0.3s ease-out;
            overflow: hidden;
        }

        .collapsed {
            max-height: 0;
        }

        .expanded {
            max-height: 5000px; /* Valeur suffisamment grande pour contenir tout le contenu */
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div id="app" v-cloak>
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gradient">
                            QuickEsti 🚀
                        </h1>
                        <span class="ml-3 text-sm text-gray-500 dark:text-gray-400">
                            Estimation de projet web
                        </span>
                    </div>
                    
                    <!-- Version -->
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600 dark:text-gray-300">
                            Version MVP
                        </span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Hero Section -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl">
                    Estimez votre projet web
                    <span class="text-gradient">en quelques clics</span>
                </h2>
                <p class="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    QuickEsti vous aide à calculer le temps et le coût de votre projet web en prenant en compte 
                    vos contraintes, technologies et objectifs. Simple, rapide et pensé pour les développeurs et entreprises.
                </p>
            </div>

            <!-- Estimation Form Container -->
            <div class="">
                <estimation-form></estimation-form>
            </div>
        </main>

        <!-- Bouton toggle dark mode en bas à droite -->
        <div class="fixed right-4 bottom-4 z-[9999]">
            <button id="theme-toggle" type="button"
                class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 transition-colors duration-300 bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-600"
                title="Basculer le mode sombre">
                <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                </svg>
                <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                        fill-rule="evenodd" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>

        <!-- Footer -->
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="text-center">
                    <p class="text-gray-600 dark:text-gray-400">
                        © 2024 QuickEsti - Outil d'estimation de projet web
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
                        Développé avec Vue.js, Tailwind CSS et Flowbite
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Flowbite JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    
    <!-- Components -->
    <script src="./js/components/UserTypeSelector.js"></script>
    <!-- Freelance Components -->
    <script src="./js/components/FreelanceBasics.js"></script>
    <script src="./js/components/FreelanceConstraints.js"></script>
    <script src="./js/components/FreelanceFeatures.js"></script>
    <script src="./js/components/FreelanceDeliverables.js"></script>
    <script src="./js/components/FreelanceObjectives.js"></script>
    <!-- Enterprise Components -->
    <script src="./js/components/EnterpriseBasics.js"></script>
    <script src="./js/components/EnterpriseStructure.js"></script>
    <script src="./js/components/EnterpriseFunctionalities.js"></script>
    <script src="./js/components/EnterpriseDeliverables.js"></script>
    <script src="./js/components/EnterpriseObjectives.js"></script>
    <script src="./js/components/EnterprisePricing.js"></script>
    <!-- Results Component -->
    <script src="./js/components/EstimationResults.js"></script>
    <!-- Tooltip Component -->
    <script src="./js/components/Tooltip.js"></script>
    <!-- Main Component -->
    <script src="./js/components/EstimationForm.js"></script>
    
    <!-- Main App -->
    <script src="./js/app.js"></script>

    <!-- Dark Mode Toggle Script -->
    <script>
        // Initialisation du dark mode
        function initDarkMode() {
            var themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
            var themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');

            // Afficher l'icône appropriée selon les paramètres précédents
            if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
                themeToggleLightIcon.classList.remove('hidden');
            } else {
                document.documentElement.classList.remove('dark');
                themeToggleDarkIcon.classList.remove('hidden');
            }

            var themeToggleBtn = document.getElementById('theme-toggle');

            themeToggleBtn.addEventListener('click', function () {
                // Basculer les icônes
                themeToggleDarkIcon.classList.toggle('hidden');
                themeToggleLightIcon.classList.toggle('hidden');

                // Gérer le basculement du thème
                if (localStorage.getItem('color-theme')) {
                    if (localStorage.getItem('color-theme') === 'light') {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('color-theme', 'dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                        localStorage.setItem('color-theme', 'light');
                    }
                } else {
                    if (document.documentElement.classList.contains('dark')) {
                        document.documentElement.classList.remove('dark');
                        localStorage.setItem('color-theme', 'light');
                    } else {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('color-theme', 'dark');
                    }
                }
            });
        }

        // Initialiser le dark mode quand le DOM est chargé
        document.addEventListener('DOMContentLoaded', initDarkMode);
    </script>
</body>
</html>
