<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickEsti - Estimation de projet web</title>
    <meta name="description" content="Estimez rapidement le temps et le coût de votre projet web avec QuickEsti. Outil d'estimation intelligent pour développeurs et entreprises.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Flowbite CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Custom CSS -->
    <style>
        [v-cloak] { display: none; }
        
        /* Custom Tailwind config */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div id="app" v-cloak>
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gradient">
                            QuickEsti 🚀
                        </h1>
                        <span class="ml-3 text-sm text-gray-500 dark:text-gray-400">
                            Estimation de projet web
                        </span>
                    </div>
                    
                    <!-- Dark mode toggle (optionnel pour plus tard) -->
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600 dark:text-gray-300">
                            Version MVP
                        </span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Hero Section -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl">
                    Estimez votre projet web
                    <span class="text-gradient">en quelques clics</span>
                </h2>
                <p class="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    QuickEsti vous aide à calculer le temps et le coût de votre projet web en prenant en compte 
                    vos contraintes, technologies et objectifs. Simple, rapide et pensé pour les développeurs et entreprises.
                </p>
            </div>

            <!-- Estimation Form Container -->
            <div class="max-w-4xl mx-auto">
                <estimation-form></estimation-form>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="text-center">
                    <p class="text-gray-600 dark:text-gray-400">
                        © 2024 QuickEsti - Outil d'estimation de projet web
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
                        Développé avec Vue.js, Tailwind CSS et Flowbite
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Flowbite JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    
    <!-- Components -->
    <script src="./js/components/UserTypeSelector.js"></script>
    <!-- Freelance Components -->
    <script src="./js/components/FreelanceBasics.js"></script>
    <script src="./js/components/FreelanceConstraints.js"></script>
    <script src="./js/components/FreelanceFeatures.js"></script>
    <script src="./js/components/FreelanceDeliverables.js"></script>
    <script src="./js/components/FreelanceObjectives.js"></script>
    <!-- Enterprise Components -->
    <script src="./js/components/EnterpriseBasics.js"></script>
    <script src="./js/components/EnterpriseStructure.js"></script>
    <script src="./js/components/EnterpriseFunctionalities.js"></script>
    <script src="./js/components/EnterpriseDeliverables.js"></script>
    <script src="./js/components/EnterpriseObjectives.js"></script>
    <script src="./js/components/EnterprisePricing.js"></script>
    <!-- Main Component -->
    <script src="./js/components/EstimationForm.js"></script>
    
    <!-- Main App -->
    <script src="./js/app.js"></script>
</body>
</html>
