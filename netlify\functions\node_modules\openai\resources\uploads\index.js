"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Uploads = exports.Parts = void 0;
var parts_1 = require("./parts.js");
Object.defineProperty(exports, "Parts", { enumerable: true, get: function () { return parts_1.Parts; } });
var uploads_1 = require("./uploads.js");
Object.defineProperty(exports, "Uploads", { enumerable: true, get: function () { return uploads_1.Uploads; } });
//# sourceMappingURL=index.js.map