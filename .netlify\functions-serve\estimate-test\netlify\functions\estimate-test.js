// netlify/functions/estimate-test.js
var allowedOrigins = [
  "http://localhost:8000",
  "https://quickesti.netlify.app",
  "https://quickesti.fr"
];
function generateTestResponse(data, userType) {
  if (userType === "freelance") {
    return {
      "estimation": {
        "duree_jours": 25,
        "duree_semaines": 5,
        "cout_total": 12500,
        "tjm_recommande": 500,
        "marge_projetee": 2500,
        "complexite": "Moyenne"
      },
      "breakdown": {
        "developpement": 18,
        "design_ui": 3,
        "integration": 2,
        "tests": 1,
        "deploiement": 1
      },
      "recommandations": [
        "Pr\xE9voir une marge de s\xE9curit\xE9 de 20% pour les impr\xE9vus",
        "Consid\xE9rer l'utilisation de frameworks modernes pour acc\xE9l\xE9rer le d\xE9veloppement",
        "Planifier des points de validation r\xE9guliers avec le client"
      ],
      "risques": [
        "D\xE9lai serr\xE9 qui pourrait impacter la qualit\xE9",
        "Complexit\xE9 des int\xE9grations API externes"
      ]
    };
  } else {
    return {
      "estimation": {
        "duree_jours_homme": 45,
        "duree_calendaire_semaines": 8,
        "cout_total": 35e3,
        "cout_par_profil": {
          "developpeur": 25e3,
          "designer": 6e3,
          "chef_projet": 4e3
        },
        "marge_appliquee": 7e3,
        "complexite": "\xC9lev\xE9e"
      },
      "breakdown": {
        "analyse_specs": 5,
        "developpement": 25,
        "design_ux_ui": 8,
        "integration": 4,
        "tests_qa": 2,
        "deploiement_devops": 1
      },
      "recommandations": [
        "Mettre en place une m\xE9thodologie Agile avec sprints de 2 semaines",
        "Pr\xE9voir des tests utilisateurs d\xE8s les premi\xE8res it\xE9rations",
        "Consid\xE9rer une architecture microservices pour la scalabilit\xE9"
      ],
      "risques": [
        "Coordination complexe entre \xE9quipes multiples",
        "Int\xE9grations avec syst\xE8mes legacy potentiellement probl\xE9matiques",
        "Mont\xE9e en charge non test\xE9e"
      ]
    };
  }
}
exports.handler = async function(event) {
  const origin = event.headers.origin;
  if (event.httpMethod === "OPTIONS") {
    if (allowedOrigins.includes(origin)) {
      return {
        statusCode: 200,
        headers: {
          "Access-Control-Allow-Origin": origin,
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type"
        },
        body: ""
      };
    } else {
      return {
        statusCode: 403,
        body: "Origin non autoris\xE9e"
      };
    }
  }
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers: {
        "Access-Control-Allow-Origin": allowedOrigins.includes(origin) ? origin : "",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ error: "M\xE9thode non autoris\xE9e" })
    };
  }
  try {
    const body = JSON.parse(event.body);
    if (!body.userType || !body.data) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": allowedOrigins.includes(origin) ? origin : "",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ error: "Donn\xE9es manquantes (userType et data requis)" })
      };
    }
    console.log("Donn\xE9es re\xE7ues pour test:", body);
    await new Promise((resolve) => setTimeout(resolve, 2e3));
    const testResult = generateTestResponse(body.data, body.userType);
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": allowedOrigins.includes(origin) ? origin : "",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        success: true,
        userType: body.userType,
        estimation: testResult,
        isTest: true
      })
    };
  } catch (error) {
    console.error("Erreur lors du test :", error.message);
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": allowedOrigins.includes(origin) ? origin : "",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ error: "Erreur serveur lors du test" })
    };
  }
};
//# sourceMappingURL=estimate-test.js.map
