{"version": 3, "sources": ["file:///D:/Development/Saas/QuickEsti/QuickEsti_vuejs/netlify/functions/estimate-test.js"], "sourceRoot": "C:/Users/<USER>/AppData/Local/Temp/tmp-30408-5cXk38nyHC6Y", "sourcesContent": ["// estimate-test.js - Version de test sans OpenAI pour valider l'intégration\nconst allowedOrigins = [\n  \"http://localhost:8000\",\n  \"https://quickesti.netlify.app\",\n  \"https://quickesti.fr\"\n];\n\n// Fonction pour générer une réponse de test\nfunction generateTestResponse(data, userType) {\n  if (userType === 'freelance') {\n    return {\n      \"estimation\": {\n        \"duree_jours\": 25,\n        \"duree_semaines\": 5,\n        \"cout_total\": 12500,\n        \"tjm_recommande\": 500,\n        \"marge_projetee\": 2500,\n        \"complexite\": \"Moyenne\"\n      },\n      \"breakdown\": {\n        \"developpement\": 18,\n        \"design_ui\": 3,\n        \"integration\": 2,\n        \"tests\": 1,\n        \"deploiement\": 1\n      },\n      \"recommandations\": [\n        \"Prévoir une marge de sécurité de 20% pour les imprévus\",\n        \"Considérer l'utilisation de frameworks modernes pour accélérer le développement\",\n        \"Planifier des points de validation réguliers avec le client\"\n      ],\n      \"risques\": [\n        \"Délai serré qui pourrait impacter la qualité\",\n        \"Complexité des intégrations API externes\"\n      ]\n    };\n  } else {\n    return {\n      \"estimation\": {\n        \"duree_jours_homme\": 45,\n        \"duree_calendaire_semaines\": 8,\n        \"cout_total\": 35000,\n        \"cout_par_profil\": {\n          \"developpeur\": 25000,\n          \"designer\": 6000,\n          \"chef_projet\": 4000\n        },\n        \"marge_appliquee\": 7000,\n        \"complexite\": \"Élevée\"\n      },\n      \"breakdown\": {\n        \"analyse_specs\": 5,\n        \"developpement\": 25,\n        \"design_ux_ui\": 8,\n        \"integration\": 4,\n        \"tests_qa\": 2,\n        \"deploiement_devops\": 1\n      },\n      \"recommandations\": [\n        \"Mettre en place une méthodologie Agile avec sprints de 2 semaines\",\n        \"Prévoir des tests utilisateurs dès les premières itérations\",\n        \"Considérer une architecture microservices pour la scalabilité\"\n      ],\n      \"risques\": [\n        \"Coordination complexe entre équipes multiples\",\n        \"Intégrations avec systèmes legacy potentiellement problématiques\",\n        \"Montée en charge non testée\"\n      ]\n    };\n  }\n}\n\nexports.handler = async function(event) {\n  const origin = event.headers.origin;\n\n  // Gestion des pré-requêtes CORS (OPTIONS)\n  if (event.httpMethod === \"OPTIONS\") {\n    if (allowedOrigins.includes(origin)) {\n      return {\n        statusCode: 200,\n        headers: {\n          \"Access-Control-Allow-Origin\": origin,\n          \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n          \"Access-Control-Allow-Headers\": \"Content-Type\",\n        },\n        body: \"\",\n      };\n    } else {\n      return {\n        statusCode: 403,\n        body: \"Origin non autorisée\",\n      };\n    }\n  }\n\n  // Vérification de la méthode HTTP\n  if (event.httpMethod !== \"POST\") {\n    return {\n      statusCode: 405,\n      headers: {\n        \"Access-Control-Allow-Origin\": allowedOrigins.includes(origin) ? origin : \"\",\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({ error: \"Méthode non autorisée\" }),\n    };\n  }\n\n  try {\n    const body = JSON.parse(event.body);\n    \n    // Validation des données requises\n    if (!body.userType || !body.data) {\n      return {\n        statusCode: 400,\n        headers: {\n          \"Access-Control-Allow-Origin\": allowedOrigins.includes(origin) ? origin : \"\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ error: \"Données manquantes (userType et data requis)\" }),\n      };\n    }\n\n    console.log('Données reçues pour test:', body);\n\n    // Simulation d'un délai de traitement\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // Génération de la réponse de test\n    const testResult = generateTestResponse(body.data, body.userType);\n\n    return {\n      statusCode: 200,\n      headers: {\n        \"Access-Control-Allow-Origin\": allowedOrigins.includes(origin) ? origin : \"\",\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({ \n        success: true,\n        userType: body.userType,\n        estimation: testResult,\n        isTest: true\n      }),\n    };\n\n  } catch (error) {\n    console.error(\"Erreur lors du test :\", error.message);\n    return {\n      statusCode: 500,\n      headers: {\n        \"Access-Control-Allow-Origin\": allowedOrigins.includes(origin) ? origin : \"\",\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({ error: \"Erreur serveur lors du test\" }),\n    };\n  }\n};\n"], "mappings": ";AACA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF;AAGA,SAAS,qBAAqB,MAAM,UAAU;AAC5C,MAAI,aAAa,aAAa;AAC5B,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,cAAc;AAAA,MAChB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,qBAAqB;AAAA,QACrB,6BAA6B;AAAA,QAC7B,cAAc;AAAA,QACd,mBAAmB;AAAA,UACjB,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AAAA,QACA,mBAAmB;AAAA,QACnB,cAAc;AAAA,MAChB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,sBAAsB;AAAA,MACxB;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,QAAQ,UAAU,eAAe,OAAO;AACtC,QAAM,SAAS,MAAM,QAAQ;AAG7B,MAAI,MAAM,eAAe,WAAW;AAClC,QAAI,eAAe,SAAS,MAAM,GAAG;AACnC,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,QAClC;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAGA,MAAI,MAAM,eAAe,QAAQ;AAC/B,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,QACP,+BAA+B,eAAe,SAAS,MAAM,IAAI,SAAS;AAAA,QAC1E,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU,EAAE,OAAO,8BAAwB,CAAC;AAAA,IACzD;AAAA,EACF;AAEA,MAAI;AACF,UAAM,OAAO,KAAK,MAAM,MAAM,IAAI;AAGlC,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,MAAM;AAChC,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,+BAA+B,eAAe,SAAS,MAAM,IAAI,SAAS;AAAA,UAC1E,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,OAAO,kDAA+C,CAAC;AAAA,MAChF;AAAA,IACF;AAEA,YAAQ,IAAI,mCAA6B,IAAI;AAG7C,UAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,UAAM,aAAa,qBAAqB,KAAK,MAAM,KAAK,QAAQ;AAEhE,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,QACP,+BAA+B,eAAe,SAAS,MAAM,IAAI,SAAS;AAAA,QAC1E,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,MAAM,OAAO;AACpD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,QACP,+BAA+B,eAAe,SAAS,MAAM,IAAI,SAAS;AAAA,QAC1E,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU,EAAE,OAAO,8BAA8B,CAAC;AAAA,IAC/D;AAAA,EACF;AACF;", "names": []}