{"version": 3, "file": "ResponseStream.mjs", "sourceRoot": "", "sources": ["../../src/lib/responses/ResponseStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;OASO,EAAE,iBAAiB,EAAE,WAAW,EAAE;OAElC,EAAmB,WAAW,EAAE;OAEhC,EAAE,kBAAkB,EAAwB;AAkDnD,MAAM,OAAO,cACX,SAAQ,WAA2B;IAOnC,YAAY,MAAsC;QAChD,KAAK,EAAE,CAAC;;QALV,yCAAwC;QACxC,0DAA+C;QAC/C,gDAAoD;QAIlD,uBAAA,IAAI,0BAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,cAAc,CACnB,MAAc,EACd,MAA4B,EAC5B,OAA6B;QAE7B,MAAM,MAAM,GAAG,IAAI,cAAc,CAAU,MAAuC,CAAC,CAAC;QACpF,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CACf,MAAM,CAAC,yBAAyB,CAAC,MAAM,EAAE,MAAM,EAAE;YAC/C,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE;SACxE,CAAC,CACH,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IA2ES,KAAK,CAAC,yBAAyB,CACvC,MAAc,EACd,MAA4B,EAC5B,OAA6B;QAE7B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,+DAAc,MAAlB,IAAI,CAAgB,CAAC;QAErB,IAAI,MAA+C,CAAC;QACpD,IAAI,cAAc,GAAkB,IAAI,CAAC;QACzC,IAAI,aAAa,IAAI,MAAM,EAAE;YAC3B,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CACtC,MAAM,CAAC,WAAW,EAClB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,CAC7D,CAAC;YACF,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;SAChD;aAAM;YACL,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CACpC,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC/C,CAAC;SACH;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,uBAAA,IAAI,2DAAU,MAAd,IAAI,EAAW,KAAK,EAAE,cAAc,CAAC,CAAC;SACvC;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,OAAO,uBAAA,IAAI,6DAAY,MAAhB,IAAI,CAAc,CAAC;IAC5B,CAAC;IAiED;QA7KE,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,uBAAA,IAAI,2CAA4B,SAAS,MAAA,CAAC;IAC5C,CAAC,+DAEwC,KAA0B,EAAE,cAA6B;QAChG,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QAEvB,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,KAAkD,EAAE,EAAE;YACrF,IAAI,cAAc,IAAI,IAAI,IAAI,KAAK,CAAC,eAAe,GAAG,cAAc,EAAE;gBACpE,IAAI,CAAC,KAAK,CAAC,IAAW,EAAE,KAAK,CAAC,CAAC;aAChC;QACH,CAAC,CAAC;QAEF,MAAM,QAAQ,GAAG,uBAAA,IAAI,qEAAoB,MAAxB,IAAI,EAAqB,KAAK,CAAC,CAAC;QACjD,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1B,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,4BAA4B,CAAC,CAAC;gBACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;iBACxE;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;oBAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,EAAE;wBACZ,MAAM,IAAI,WAAW,CAAC,4BAA4B,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;qBAC1E;oBACD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE;wBAClC,MAAM,IAAI,WAAW,CAAC,6CAA6C,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;qBACpF;oBAED,SAAS,CAAC,4BAA4B,EAAE;wBACtC,GAAG,KAAK;wBACR,QAAQ,EAAE,OAAO,CAAC,IAAI;qBACvB,CAAC,CAAC;iBACJ;gBACD,MAAM;aACP;YACD,KAAK,wCAAwC,CAAC,CAAC;gBAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;iBACxE;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE;oBACnC,SAAS,CAAC,wCAAwC,EAAE;wBAClD,GAAG,KAAK;wBACR,QAAQ,EAAE,MAAM,CAAC,SAAS;qBAC3B,CAAC,CAAC;iBACJ;gBACD,MAAM;aACP;YACD;gBACE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7B,MAAM;SACT;IACH,CAAC;QAGC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,WAAW,CAAC,yCAAyC,CAAC,CAAC;SAClE;QACD,MAAM,QAAQ,GAAG,uBAAA,IAAI,+CAAyB,CAAC;QAC/C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,WAAW,CAAC,0CAA0C,CAAC,CAAC;SACnE;QACD,uBAAA,IAAI,2CAA4B,SAAS,MAAA,CAAC;QAC1C,MAAM,cAAc,GAAG,gBAAgB,CAAU,QAAQ,EAAE,uBAAA,IAAI,8BAAQ,CAAC,CAAC;QACzE,uBAAA,IAAI,iCAAkB,cAAc,MAAA,CAAC;QAErC,OAAO,cAAc,CAAC;IACxB,CAAC,mFAwCmB,KAA0B;QAC5C,IAAI,QAAQ,GAAG,uBAAA,IAAI,+CAAyB,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACrC,MAAM,IAAI,WAAW,CACnB,6EAA6E,KAAK,CAAC,IAAI,EAAE,CAC1F,CAAC;aACH;YACD,QAAQ,GAAG,uBAAA,IAAI,2CAA4B,KAAK,CAAC,QAAQ,MAAA,CAAC;YAC1D,OAAO,QAAQ,CAAC;SACjB;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,4BAA4B,CAAC,CAAC;gBACjC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;aACP;YACD,KAAK,6BAA6B,CAAC,CAAC;gBAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;iBACxE;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;oBAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACjC;gBACD,MAAM;aACP;YACD,KAAK,4BAA4B,CAAC,CAAC;gBACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;iBACxE;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;oBAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,EAAE;wBACZ,MAAM,IAAI,WAAW,CAAC,4BAA4B,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;qBAC1E;oBACD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE;wBAClC,MAAM,IAAI,WAAW,CAAC,6CAA6C,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;qBACpF;oBACD,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC;iBAC7B;gBACD,MAAM;aACP;YACD,KAAK,wCAAwC,CAAC,CAAC;gBAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;iBACxE;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE;oBACnC,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC;iBACjC;gBACD,MAAM;aACP;YACD,KAAK,oBAAoB,CAAC,CAAC;gBACzB,uBAAA,IAAI,2CAA4B,KAAK,CAAC,QAAQ,MAAA,CAAC;gBAC/C,MAAM;aACP;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,EAEA,MAAM,CAAC,aAAa,EAAC;QACpB,MAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,MAAM,SAAS,GAGT,EAAE,CAAC;QACT,IAAI,IAAI,GAAG,KAAK,CAAC;QAEjB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAClB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,KAAK,IAAkD,EAAE;gBAC7D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;oBACrB,IAAI,IAAI,EAAE;wBACR,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;qBACzC;oBACD,OAAO,IAAI,OAAO,CAAkC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACtE,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CACpC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;iBAC/F;gBACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAG,CAAC;gBACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACvC,CAAC;YACD,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC1C,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,QAAQ,GAAG,uBAAA,IAAI,qCAAe,CAAC;QACrC,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,WAAW,CAAC,iDAAiD,CAAC,CAAC;QACxF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,SAAS,gBAAgB,CACvB,QAAkB,EAClB,MAAsC;IAEtC,OAAO,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC"}