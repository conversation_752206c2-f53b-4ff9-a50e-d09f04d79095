<template>
    <!-- Header with Navigation -->
    <header>
        <nav class="bg-white border-gray-200 px-4 lg:px-6 py-2.5 dark:bg-gray-800">
            <div class="flex flex-wrap justify-between items-center mx-auto max-w-screen-xl">
                <RouterLink to="/" class="flex items-center">
                    <img src="https://flowbite.com/docs/images/logo.svg" class="mr-3 h-6 sm:h-9"
                        alt="AirFryTime Logo" />
                    <span class="self-center text-xl font-semibold text-gray-700 whitespace-nowrap dark:text-white">AirFryTime
                        🍗</span>
                </RouterLink>
                <div class="flex items-center lg:order-2">
                    <RouterLink to="/#"
                        class="inline-flex justify-center items-center py-3 px-5 text-white font-medium text-center rounded-lg bg-blue-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-900">
                        Contact
                    </RouterLink>
                    <button @click="toggleMenu" type="button"
                        class="inline-flex items-center p-2 ml-1 text-sm text-gray-500 rounded-lg lg:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
                        :aria-expanded="isMenuOpen">
                        <span class="sr-only">Ouvrir le menu</span>
                        <svg :class="{'hidden': isMenuOpen, 'block': !isMenuOpen}" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd"
                                d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <svg :class="{'hidden': !isMenuOpen, 'block': isMenuOpen}" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
                <div :class="{'hidden': !isMenuOpen}" class="justify-between items-center w-full lg:flex lg:w-auto lg:order-1" id="mobile-menu-2">
                    <ul class="flex flex-col mt-4 font-medium lg:flex-row lg:space-x-8 lg:mt-0">
                        <li>
                            <RouterLink to="/"
                                class="block py-2 pr-4 pl-3 text-gray-700 rounded bg-primary-700 lg:bg-transparent lg:text-primary-700 lg:p-0 dark:text-white"
                                aria-current="page">Accueil</RouterLink>
                        </li>
                        <li>
                            <RouterLink to="/calculateur-cuisson-air-fryer"
                                class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-primary-700 lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">
                                Calculateur</RouterLink>
                        </li>
                        <li>
                            <RouterLink to="/fonctionnalites"
                                class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-primary-700 lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">
                                Fonctionnalités</RouterLink>
                        </li>
                        <li>
                            <RouterLink to="/#a-propos"
                                class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-primary-700 lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">
                                A Propos</RouterLink>
                        </li>
                        <li>
                            <RouterLink to="/blog"
                                class="block py-2 pr-4 pl-3 text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-primary-700 lg:p-0 dark:text-gray-400 lg:dark:hover:text-white dark:hover:bg-gray-700 dark:hover:text-white lg:dark:hover:bg-transparent dark:border-gray-700">
                                Blog</RouterLink>
                        </li>
                        <!-- Lien de bypass visible seulement en mode développement -->
                        <li v-if="showBypassLink">
                            <RouterLink to="/calculateur-cuisson-air-fryer/bypass-mode"
                                class="block py-2 pr-4 pl-3 text-yellow-600 border-b border-gray-100 hover:bg-yellow-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-yellow-700 lg:p-0 dark:text-yellow-400 lg:dark:hover:text-yellow-300 dark:hover:bg-gray-700 dark:hover:text-yellow-300 lg:dark:hover:bg-transparent dark:border-gray-700 font-medium">
                                🔐 Admin</RouterLink>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <!-- Header with Navigation -->
</template>

<script setup>
import { ref, computed } from 'vue'
import { RouterLink } from 'vue-router'

const isMenuOpen = ref(false)

const toggleMenu = () => {
    isMenuOpen.value = !isMenuOpen.value
}

// Afficher le lien de bypass seulement si activé dans les variables d'environnement
const showBypassLink = computed(() => {
    return import.meta.env.VITE_ENABLE_BYPASS === 'true'
})
</script>